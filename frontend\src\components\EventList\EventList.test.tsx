/**
 * EventList Component Tests
 * Comprehensive test suite following TDD approach
 * Tests all functionality, error states, and accessibility
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EventList } from './EventList';
import * as apiService from '../../services/api';
import { IContact } from '../../types/api';

// Mock the API service
jest.mock('../../services/api');
const mockGetEvents = apiService.getEvents as jest.MockedFunction<typeof apiService.getEvents>;

// Mock date-fns to ensure consistent test results
jest.mock('date-fns', () => ({
  ...jest.requireActual('date-fns'),
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'MMM dd, yyyy') return 'Dec 25, 2025';
    if (formatStr === 'yyyy-MM-dd') return '2025-12-25';
    return 'Dec 25, 2025';
  }),
  parseISO: jest.fn((dateStr) => new Date(dateStr)),
  differenceInDays: jest.fn((date1, date2) => {
    // Mock to return 30 days until event, 23 days until reminder
    if (date1.toString().includes('2025-12-25')) return 30;
    if (date1.toString().includes('2025-12-18')) return 23;
    return 30;
  }),
  addDays: jest.fn((date, days) => new Date('2025-12-18')),
}));

// Sample test data
const mockEvents: IContact[] = [
  {
    id: '1',
    contact_name: 'John Doe',
    contact_phone: '+1234567890',
    event_date: '2025-12-25',
    lead_time_days: 7,
    reminder_sent: false,
  },
  {
    id: '2',
    contact_name: 'Jane Smith',
    contact_phone: '+0987654321',
    event_date: '2025-12-31',
    lead_time_days: 14,
    reminder_sent: true,
  },
];

describe('EventList Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // ============================================================================
  // BASIC RENDERING TESTS
  // ============================================================================

  test('renders component with title', () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    expect(screen.getByText('Upcoming Events')).toBeInTheDocument();
  });

  test('renders loading state initially', () => {
    mockGetEvents.mockImplementation(() => new Promise(() => {})); // Never resolves
    render(<EventList />);
    
    expect(screen.getByText('Loading events...')).toBeInTheDocument();
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  test('renders events after loading', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  test('displays error message when API fails', async () => {
    const errorMessage = 'Failed to fetch events';
    mockGetEvents.mockRejectedValue(new Error(errorMessage));
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByRole('alert')).toBeInTheDocument();
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  test('allows dismissing error message', async () => {
    mockGetEvents.mockRejectedValue(new Error('Test error'));
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByRole('alert')).toBeInTheDocument();
    });

    const dismissButton = screen.getByLabelText('Dismiss error');
    fireEvent.click(dismissButton);
    
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  test('displays external error prop', () => {
    mockGetEvents.mockResolvedValue([]);
    render(<EventList error="External error message" />);
    
    expect(screen.getByText('External error message')).toBeInTheDocument();
  });

  // ============================================================================
  // EMPTY STATE TESTS
  // ============================================================================

  test('displays empty state when no events', async () => {
    mockGetEvents.mockResolvedValue([]);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('No events found. Import some contacts to get started.')).toBeInTheDocument();
      expect(screen.getByText('Use the Import Form to add your first contact and event.')).toBeInTheDocument();
    });
  });

  test('displays filtered empty state when events exist but none match filter', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Filter by search term that doesn't match
    const searchInput = screen.getByLabelText('Search events');
    await userEvent.type(searchInput, 'NonExistentName');
    
    await waitFor(() => {
      expect(screen.getByText('No events match your current filters.')).toBeInTheDocument();
    });
  });

  // ============================================================================
  // FILTERING TESTS
  // ============================================================================

  test('filters events by search term', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    const searchInput = screen.getByLabelText('Search events');
    await userEvent.type(searchInput, 'John');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });
  });

  test('filters events by phone number', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    const searchInput = screen.getByLabelText('Search events');
    await userEvent.type(searchInput, '+1234567890');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });
  });

  test('filters events by status', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    const statusFilter = screen.getByLabelText('Filter by status');
    await userEvent.selectOptions(statusFilter, 'sent');
    
    await waitFor(() => {
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });
  });

  // ============================================================================
  // SORTING TESTS
  // ============================================================================

  test('sorts events by contact name', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const sortButton = screen.getByLabelText('Sort by Contact Name');
    fireEvent.click(sortButton);
    
    // Verify sorting button shows correct icon
    expect(sortButton).toHaveTextContent('Contact ↑');
  });

  test('toggles sort direction on repeated clicks', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const sortButton = screen.getByLabelText('Sort by Contact Name');
    
    // First click - ascending
    fireEvent.click(sortButton);
    expect(sortButton).toHaveTextContent('Contact ↑');
    
    // Second click - descending
    fireEvent.click(sortButton);
    expect(sortButton).toHaveTextContent('Contact ↓');
  });

  // ============================================================================
  // INTERACTION TESTS
  // ============================================================================

  test('calls onEventSelect when event is clicked', async () => {
    const mockOnEventSelect = jest.fn();
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList onEventSelect={mockOnEventSelect} />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const eventItem = screen.getByLabelText('Event for John Doe on undefined');
    fireEvent.click(eventItem);

    expect(mockOnEventSelect).toHaveBeenCalledWith(mockEvents[0]);
  });

  test('handles keyboard navigation on event items', async () => {
    const mockOnEventSelect = jest.fn();
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList onEventSelect={mockOnEventSelect} />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const eventItem = screen.getByLabelText('Event for John Doe on undefined');
    eventItem.focus();
    fireEvent.keyDown(eventItem, { key: 'Enter' });

    expect(mockOnEventSelect).toHaveBeenCalledWith(mockEvents[0]);
  });

  test('calls onRefresh when refresh button is clicked', async () => {
    const mockOnRefresh = jest.fn();
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList onRefresh={mockOnRefresh} />);

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const refreshButton = screen.getByLabelText('Refresh events');
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(mockOnRefresh).toHaveBeenCalled();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  test('has proper ARIA labels and roles', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByRole('region', { name: 'Events List' })).toBeInTheDocument();
      expect(screen.getByLabelText('Search events')).toBeInTheDocument();
      expect(screen.getByLabelText('Filter by status')).toBeInTheDocument();
      expect(screen.getByLabelText('Refresh events')).toBeInTheDocument();
    });
  });

  test('supports keyboard navigation', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const eventItem = screen.getByLabelText('Event for John Doe on undefined');
    expect(eventItem).toHaveAttribute('tabIndex', '0');
  });

  // ============================================================================
  // PROPS TESTS
  // ============================================================================

  test('respects maxEvents prop', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList maxEvents={1} />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });
  });

  test('hides controls when showControls is false', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList showControls={false} />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    expect(screen.queryByLabelText('Search events')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Filter by status')).not.toBeInTheDocument();
  });

  test('hides refresh button when showRefreshButton is false', async () => {
    mockGetEvents.mockResolvedValue(mockEvents);
    render(<EventList showRefreshButton={false} />);
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    expect(screen.queryByLabelText('Refresh events')).not.toBeInTheDocument();
  });
});
