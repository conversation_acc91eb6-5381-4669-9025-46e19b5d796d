/**
 * EventList Component
 * Displays all reminders with filter/sort options, loading states, and error handling
 * 
 * Features:
 * - Real-time event loading from API
 * - Filter by status, search term, and date range
 * - Sort by event date, contact name, or lead time
 * - Loading states and error handling
 * - Accessibility compliance (ARIA labels, keyboard navigation)
 * - Responsive design considerations
 * - Empty state handling
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { format, parseISO, differenceInDays, addDays } from 'date-fns';
import { getEvents } from '../../services/api';
import { IContact } from '../../types/api';
import {
  IEventListProps,
  IEventListState,
  IEventDisplayData,
  IFilterConfig,
  SortField,
  SortDirection,
  FilterStatus,
  DEFAULT_EVENT_LIST_STATE,
  SORT_FIELD_LABELS,
  FILTER_STATUS_LABELS,
  EVENT_LIST_MESSAGES,
} from './types';
import './EventList.css';

export const EventList: React.FC<IEventListProps> = ({
  onEventSelect,
  onRefresh,
  isLoading: externalLoading = false,
  error: externalError,
  className = '',
  showRefreshButton = true,
  showControls = true,
  maxEvents,
}) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [state, setState] = useState<IEventListState>(DEFAULT_EVENT_LIST_STATE);

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const isLoading = externalLoading || state.isLoading;
  const error = externalError || state.error;

  // Transform events to display data with calculated fields
  const eventsWithDisplayData = useMemo((): IEventDisplayData[] => {
    return state.events.map((event) => {
      const eventDate = parseISO(event.event_date);
      const today = new Date();
      const daysUntilEvent = differenceInDays(eventDate, today);
      const reminderDate = format(
        addDays(eventDate, -event.lead_time_days),
        'yyyy-MM-dd'
      );
      const reminderDateObj = parseISO(reminderDate);
      const daysUntilReminder = differenceInDays(reminderDateObj, today);

      return {
        ...event,
        daysUntilEvent,
        reminderDate,
        isOverdue: daysUntilReminder < 0 && !event.reminder_sent,
        isDueToday: daysUntilReminder === 0 && !event.reminder_sent,
        displayDate: format(eventDate, 'MMM dd, yyyy'),
        statusText: event.reminder_sent
          ? 'Reminder Sent'
          : daysUntilReminder < 0
          ? 'Overdue'
          : daysUntilReminder === 0
          ? 'Due Today'
          : `Due in ${daysUntilReminder} days`,
      };
    });
  }, [state.events]);

  // Apply filters and sorting
  const processedEvents = useMemo((): IEventDisplayData[] => {
    let filtered = eventsWithDisplayData;

    // Apply status filter
    if (state.filterConfig.status !== 'all') {
      filtered = filtered.filter((event) => {
        switch (state.filterConfig.status) {
          case 'pending':
            return !event.reminder_sent;
          case 'sent':
            return event.reminder_sent;
          default:
            return true;
        }
      });
    }

    // Apply search filter
    if (state.filterConfig.searchTerm) {
      const searchTerm = state.filterConfig.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (event) =>
          event.contact_name.toLowerCase().includes(searchTerm) ||
          event.contact_phone.includes(searchTerm)
      );
    }

    // Apply date range filter
    if (state.filterConfig.dateRange) {
      const { start, end } = state.filterConfig.dateRange;
      filtered = filtered.filter((event) => {
        return event.event_date >= start && event.event_date <= end;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const { field, direction } = state.sortConfig;
      let aValue: any;
      let bValue: any;

      switch (field) {
        case 'event_date':
          aValue = new Date(a.event_date);
          bValue = new Date(b.event_date);
          break;
        case 'contact_name':
          aValue = a.contact_name.toLowerCase();
          bValue = b.contact_name.toLowerCase();
          break;
        case 'lead_time_days':
          aValue = a.lead_time_days;
          bValue = b.lead_time_days;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return direction === 'asc' ? 1 : -1;
      return 0;
    });

    // Apply max events limit
    if (maxEvents && maxEvents > 0) {
      filtered = filtered.slice(0, maxEvents);
    }

    return filtered;
  }, [eventsWithDisplayData, state.sortConfig, state.filterConfig, maxEvents]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const loadEvents = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await getEvents();
      
      if (Array.isArray(response)) {
        setState((prev) => ({
          ...prev,
          events: response,
          isLoading: false,
          error: null,
        }));
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : EVENT_LIST_MESSAGES.ERROR_GENERIC;
      
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    await loadEvents();
    if (onRefresh) {
      onRefresh();
    }
  }, [loadEvents, onRefresh]);

  const handleSort = useCallback((field: SortField) => {
    setState((prev) => {
      const newDirection: SortDirection =
        prev.sortConfig.field === field && prev.sortConfig.direction === 'asc'
          ? 'desc'
          : 'asc';

      return {
        ...prev,
        sortConfig: { field, direction: newDirection },
      };
    });
  }, []);

  const handleFilterChange = useCallback((updates: Partial<IFilterConfig>) => {
    setState((prev) => ({
      ...prev,
      filterConfig: { ...prev.filterConfig, ...updates },
    }));
  }, []);

  const handleEventClick = useCallback((event: IEventDisplayData) => {
    setState((prev) => ({
      ...prev,
      selectedEventId: event.id === prev.selectedEventId ? null : event.id,
    }));

    if (onEventSelect) {
      // Pass the original event object, not the enhanced display data
      const originalEvent: IContact = {
        id: event.id,
        contact_name: event.contact_name,
        contact_phone: event.contact_phone,
        event_date: event.event_date,
        lead_time_days: event.lead_time_days,
        reminder_sent: event.reminder_sent,
      };
      onEventSelect(originalEvent);
    }
  }, [onEventSelect]);

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    loadEvents();
  }, [loadEvents]);

  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  const getSortIcon = (field: SortField): string => {
    if (state.sortConfig.field !== field) return '↕️';
    return state.sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  const getStatusClassName = (event: IEventDisplayData): string => {
    if (event.reminder_sent) return 'event-list__status--sent';
    if (event.isOverdue) return 'event-list__status--overdue';
    if (event.isDueToday) return 'event-list__status--due-today';
    return 'event-list__status--pending';
  };

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const renderControls = () => {
    if (!showControls) return null;

    return (
      <div className="event-list__controls">
        {/* Search Input */}
        <div className="event-list__search">
          <input
            type="text"
            placeholder="Search contacts..."
            value={state.filterConfig.searchTerm}
            onChange={(e) => handleFilterChange({ searchTerm: e.target.value })}
            className="event-list__search-input"
            aria-label="Search events"
          />
        </div>

        {/* Status Filter */}
        <div className="event-list__filter">
          <select
            value={state.filterConfig.status}
            onChange={(e) => handleFilterChange({ status: e.target.value as FilterStatus })}
            className="event-list__filter-select"
            aria-label="Filter by status"
          >
            {Object.entries(FILTER_STATUS_LABELS).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </select>
        </div>

        {/* Refresh Button */}
        {showRefreshButton && (
          <button
            type="button"
            onClick={handleRefresh}
            disabled={isLoading}
            className="event-list__refresh-button"
            aria-label="Refresh events"
          >
            {isLoading ? '⟳' : '↻'} Refresh
          </button>
        )}
      </div>
    );
  };

  const renderHeader = () => (
    <div className="event-list__header">
      <button
        type="button"
        onClick={() => handleSort('contact_name')}
        className="event-list__sort-button"
        aria-label={`Sort by ${SORT_FIELD_LABELS.contact_name}`}
      >
        Contact {getSortIcon('contact_name')}
      </button>
      <button
        type="button"
        onClick={() => handleSort('event_date')}
        className="event-list__sort-button"
        aria-label={`Sort by ${SORT_FIELD_LABELS.event_date}`}
      >
        Event Date {getSortIcon('event_date')}
      </button>
      <button
        type="button"
        onClick={() => handleSort('lead_time_days')}
        className="event-list__sort-button"
        aria-label={`Sort by ${SORT_FIELD_LABELS.lead_time_days}`}
      >
        Lead Time {getSortIcon('lead_time_days')}
      </button>
      <span className="event-list__header-status">Status</span>
    </div>
  );

  const renderEventItem = (event: IEventDisplayData) => (
    <div
      key={event.id}
      className={`event-list__item ${
        state.selectedEventId === event.id ? 'event-list__item--selected' : ''
      }`}
      onClick={() => handleEventClick(event)}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleEventClick(event);
        }
      }}
      aria-label={`Event for ${event.contact_name} on ${event.displayDate}`}
    >
      <div className="event-list__item-contact">
        <div className="event-list__contact-name">{event.contact_name}</div>
        <div className="event-list__contact-phone">{event.contact_phone}</div>
      </div>
      <div className="event-list__item-date">
        <div className="event-list__event-date">{event.displayDate}</div>
        <div className="event-list__days-until">
          {event.daysUntilEvent >= 0
            ? `In ${event.daysUntilEvent} days`
            : `${Math.abs(event.daysUntilEvent)} days ago`}
        </div>
      </div>
      <div className="event-list__item-lead-time">
        {event.lead_time_days} days
      </div>
      <div className={`event-list__item-status ${getStatusClassName(event)}`}>
        {event.statusText}
      </div>
    </div>
  );

  const renderEmptyState = () => {
    const message = state.events.length === 0
      ? EVENT_LIST_MESSAGES.NO_EVENTS
      : EVENT_LIST_MESSAGES.NO_FILTERED_EVENTS;

    return (
      <div className="event-list__empty" role="status">
        <div className="event-list__empty-icon">📅</div>
        <div className="event-list__empty-message">{message}</div>
        {state.events.length === 0 && (
          <div className="event-list__empty-hint">
            Use the Import Form to add your first contact and event.
          </div>
        )}
      </div>
    );
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <div className="event-list__error" role="alert">
        <div className="event-list__error-message">{error}</div>
        <button
          type="button"
          onClick={clearError}
          className="event-list__error-dismiss"
          aria-label="Dismiss error"
        >
          ✕
        </button>
      </div>
    );
  };

  const renderLoadingState = () => (
    <div className="event-list__loading" role="status" aria-live="polite">
      <div className="event-list__loading-spinner"></div>
      <div className="event-list__loading-message">
        {EVENT_LIST_MESSAGES.LOADING}
      </div>
    </div>
  );

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <div className={`event-list ${className}`} role="region" aria-label="Events List">
      <div className="event-list__header-section">
        <h2 className="event-list__title">Upcoming Events</h2>
        {processedEvents.length > 0 && (
          <div className="event-list__count">
            {processedEvents.length} of {state.events.length} events
          </div>
        )}
      </div>

      {renderControls()}
      {renderError()}

      {isLoading ? (
        renderLoadingState()
      ) : processedEvents.length === 0 ? (
        renderEmptyState()
      ) : (
        <div className="event-list__content">
          {renderHeader()}
          <div className="event-list__items">
            {processedEvents.map(renderEventItem)}
          </div>
        </div>
      )}
    </div>
  );
};
